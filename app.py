import streamlit as st
import plotly.express as px
from snowflake.snowpark.context import get_active_session
 
 
 
# Get the current credentials
session = get_active_session()
 
import json  # To handle JSON data
import time
from datetime import datetime
from typing import Dict, List, Optional, Tuple
 
import _snowflake  # For interacting with Snowflake-specific APIs
import pandas as pd
from snowflake.snowpark.exceptions import SnowparkSQLException
 
# List of available semantic model paths in the format: @<database>.<schema>.<stage>/<file>.yaml
AVAILABLE_SEMANTIC_MODELS_PATHS = [
    '@"TEST_DB"."PUBLIC"."MY_STAGE"/GLOBAL_SALES_FINAL.yaml',
    '@"TEST_DB"."PUBLIC"."MY_STAGE"/pizza.yaml'
]
 
# Available regional roles for role-based access control
AVAILABLE_ROLES = {
    "HRZN_CARIBBEAN_HEAD": "Caribbean",
    "HRZN_CENTRAL_AFRICA_HEAD": "Central Africa",
    "HRZN_CENTRAL_AMERICA_HEAD": "Central America",
    "HRZN_CENTRAL_ASIA_HEAD": "Central Asia",
    "HRZN_CENTRAL_US_HEAD": "Central US",
    "HRZN_EASTERN_AFRICA_HEAD": "Eastern Africa",
    "HRZN_EASTERN_ASIA_HEAD": "Eastern Asia",
    "HRZN_EASTERN_CANADA_HEAD": "Eastern Canada",
    "HRZN_NORTH_AFRICA_HEAD": "North Africa",
    "HRZN_NORTHERN_ASIA_HEAD": "Northern Asia",
    "HRZN_NORTHERN_EUROPE_HEAD": "Northern Europe",
    "HRZN_OCEANIA_HEAD": "Oceania",
    "HRZN_SOUTHEASTERN_ASIA_HEAD": "Southeastern Asia",
    "HRZN_SOUTHERN_AFRICA_HEAD": "Southern Africa",
    "HRZN_SOUTHERN_ASIA_HEAD": "Southern Asia",
    "HRZN_SOUTHERN_EUROPE_HEAD": "Southern Europe",
    "HRZN_SOUTHERN_US_HEAD": "Southern US",
    "HRZN_WESTERN_AFRICA_HEAD": "Western Africa",
    "HRZN_WESTERN_ASIA_HEAD": "Western Asia",
    "HRZN_WESTERN_CANADA_HEAD": "Western Canada",
    "HRZN_WESTERN_EUROPE_HEAD": "Western Europe",
    "HRZN_WESTERN_US_HEAD": "Western US"
}
 
API_ENDPOINT = "/api/v2/cortex/analyst/message"
API_TIMEOUT = 50000  # in milliseconds
 
 
 
def add_custom_css():
    custom_css = """
    <style>
    /* Set the overall background color for the app */
    body {
        background-color: #f4f7f6;
    }
 
    /* Style for the header */
    .streamlit-expanderHeader {
        font-size: 20px !important;
        font-weight: bold;
        color: #2a4d8d;
    }
 
    /* Style for the title */
    h1 {
        color: #2a4d8d;
        font-family: 'Roboto', sans-serif;
    }
 
    /* Customize the sidebar background */
    .css-1d391kg {
        background-color: #2a4d8d !important;
        color: white !important;
    }
 
    /* Style the buttons */
    .stButton > button {
        background-color: #CFEAE2;
        color: black;
        border-radius: 12px;
        font-weight: bold;
        transition: background-color 0.3s ease;
    }
 
    .stButton > button:hover {
        background-color: white !important;
        color: black !important;
        border: 2px solid #455F56;
    }
   
    section[data-testid="stSidebar"] .stDownloadButton > button {
        background-color: #CFEAE2 !important;
        color: black !important;
        border-radius: 12px !important;
        font-weight: bold !important;
        transition: background-color 0.3s ease !important;
        width: 100% !important;
        border: none !important;
        margin: 0.25rem 0 !important;
    }
 
    section[data-testid="stSidebar"] .stDownloadButton > button:hover {
        background-color: white !important;
        color: black !important;
        border: 2px solid #455F56 !important;
    }
 
    /* Add some padding between widgets */
    .stTextInput, .stSelectbox, .stButton {
        margin-top: 10px;
        margin-bottom: 10px;
    }
 
    /* Customize the chat bubble styles */
    .streamlit-chat-message {
        padding: 12px 18px;
        border-radius: 12px;
        margin-bottom: 10px;
        box-shadow: 0px 1px 4px rgba(0,0,0,0.1);
    }
 
 
    /* Customize user message bubbles */
    .streamlit-chat-message[data-role="user"] {
        background-color: #00BFFF;
        color: white;
    }
 
   /* More generic selector for analyst messages */
    div[data-testid="stChatMessage"]:has(div[data-testid="stChatMessageContent"]) {
    border-left: 4px solid #1d3f8a;
    background-color: #f9f9f9;
    padding: 12px 18px;
    border-radius: 12px;
    margin-bottom: 10px;
}
 
    /* Style for section headers */
    .stExpanderHeader {
        background-color: #e3f2fd;
        color: #1c3b66;
        font-weight: bold; !important
    }
 
    /* Add custom background color to selected query result section */
    .stDataFrame {
        background-color: #CFEAE2;
        border-radius: 8px;
        padding: 10px;
    }
   
    </style>
    """
    st.markdown(custom_css, unsafe_allow_html=True)
 
 
 
def main():
    # Initialize session state
    if "messages" not in st.session_state:
        reset_session_state()
    show_header_and_sidebar()
    if len(st.session_state.messages) == 0:
        process_user_input("What questions can I ask?")
    display_conversation()
    handle_user_inputs()
    handle_error_notifications()
    add_custom_css()
 
 
 
def reset_session_state():
    """Reset important session state elements."""
    st.session_state.messages = []  # List to store conversation messages
    st.session_state.active_suggestion = None  # Currently selected suggestion
    if "selected_role" not in st.session_state:
        st.session_state.selected_role = None  # Currently selected role
 
 
def switch_role(role_name: str):
    """
    Switch the current Snowflake session to the specified role.
    Note: In Streamlit in Snowflake, dynamic role switching may not be supported.
 
    Args:
        role_name (str): The name of the role to switch to.
    """
    global session
 
    # Set the role in session state for UI purposes
    st.session_state.selected_role = role_name
    region = AVAILABLE_ROLES.get(role_name, "Unknown")
 
    # Try different methods to switch roles
    try:
        # Method 1: Try using session.use_role if available
        if hasattr(session, 'use_role'):
            session.use_role(role_name)
            st.success(f"✅ Successfully switched to role: {region} ({role_name})")
            return True
    except Exception as e1:
        pass
 
    try:
        # Method 2: Try direct SQL execution
        session.sql(f"USE ROLE {role_name}").collect()
        st.success(f"✅ Successfully switched to role: {region} ({role_name})")
        return True
    except Exception as e2:
        pass
 
    try:
        # Method 3: Try using connection cursor
        if hasattr(session, '_conn') and session._conn:
            session._conn.cursor().execute(f"USE ROLE {role_name}")
            st.success(f"✅ Successfully switched to role: {region} ({role_name})")
            return True
    except Exception as e3:
        pass
 
    # If all methods fail, show informative message
    st.warning(f"⚠️ **Role Selected:** {region} ({role_name})")
    st.info("""
    💡 **Important Note:**
 
    Dynamic role switching is not supported in this Snowflake environment.
 
    **To use role-based filtering:**
    1. Manually switch to the desired role in Snowflake: `USE ROLE {role_name}`
    2. Then refresh this app
    3. Or deploy separate app instances for each role
 
    **Current behavior:**
    - Role selection is tracked for display purposes
    - Data filtering depends on your actual Snowflake session role
    - Row access policies will still apply based on your current role
    """.format(role_name=role_name))
 
    return False
 
 
def get_current_role_info():
    """Get information about the currently selected role."""
    if st.session_state.selected_role:
        role_name = st.session_state.selected_role
        region = AVAILABLE_ROLES.get(role_name, "Unknown")
        return f"🔐 Current Role: {region} ({role_name})"
    else:
        return "🔓 No role selected - Full access"
 
 
def test_current_role():
    """Test the current role by running a simple query to check data access."""
    global session
    try:
        # Test query to check current role and data access
        current_role_query = "SELECT CURRENT_ROLE() as current_role"
        role_result = session.sql(current_role_query).to_pandas()
 
        # Test data access with a simple count query - using the same tables as in your YAML
        try:
            test_query = "SELECT REGION, COUNT(*) as record_count FROM TEST_DB.PUBLIC.SALES GROUP BY REGION ORDER BY REGION LIMIT 10"
            data_result = session.sql(test_query).to_pandas()
        except Exception as e:
            st.error(f"❌ Cannot access SALES table: {str(e)}")
            # Try alternative approaches
            try:
                # Check if we can see the table at all
                show_tables_query = "SHOW TABLES LIKE 'SALES' IN SCHEMA TEST_DB.PUBLIC"
                table_check = session.sql(show_tables_query).to_pandas()
                if table_check.empty:
                    st.error("❌ SALES table not found or not accessible")
                else:
                    st.info("✅ SALES table exists but data query failed")
 
                # Try a simple count without GROUP BY
                simple_count_query = "SELECT COUNT(*) as total_count FROM TEST_DB.PUBLIC.SALES"
                count_result = session.sql(simple_count_query).to_pandas()
                st.info(f"📊 Total records in SALES table: {count_result.iloc[0]['TOTAL_COUNT']}")
 
                # Create empty dataframe for display
                data_result = pd.DataFrame(columns=['REGION', 'RECORD_COUNT'])
 
            except Exception as e2:
                st.error(f"❌ All table access attempts failed: {str(e2)}")
                data_result = pd.DataFrame(columns=['REGION', 'RECORD_COUNT'])
 
        st.success("✅ Role test completed successfully!")
 
        col1, col2 = st.columns(2)
 
        with col1:
            st.subheader("Current Snowflake Role:")
            st.dataframe(role_result, use_container_width=True)
 
        with col2:
            st.subheader("Accessible Data by Region:")
            if not data_result.empty:
                st.dataframe(data_result, use_container_width=True)
            else:
                st.warning("No data returned from SALES table")
 
        # Additional test - check if tables exist and are accessible
        try:
            table_test_query = """
            SELECT
                'SALES' as table_name, COUNT(*) as total_records
            FROM TEST_DB.PUBLIC.SALES
            UNION ALL
            SELECT
                'RETURN' as table_name, COUNT(*) as total_records
            FROM TEST_DB.PUBLIC.RETURN
            UNION ALL
            SELECT
                'REPRESENTATIVES' as table_name, COUNT(*) as total_records
            FROM TEST_DB.PUBLIC.REPRESENTATIVES
            """
            table_result = session.sql(table_test_query).to_pandas()
 
            st.subheader("Table Access Summary:")
            st.dataframe(table_result, use_container_width=True)
 
        except Exception as table_error:
            st.warning(f"⚠️ Could not access some tables: {str(table_error)}")
 
        # Role analysis
        if st.session_state.selected_role:
            expected_region = AVAILABLE_ROLES.get(st.session_state.selected_role, "Unknown")
            if not data_result.empty:
                accessible_regions = data_result['REGION'].tolist()
 
                if len(accessible_regions) == 1 and accessible_regions[0] == expected_region:
                    st.success(f"✅ **Perfect!** You can only access {expected_region} data as expected.")
                elif len(accessible_regions) > 1:
                    st.warning(f"⚠️ **Note:** You can access multiple regions: {', '.join(accessible_regions)}. "
                              f"Expected only {expected_region}. This might indicate the role policy needs adjustment or role switching didn't work.")
                else:
                    st.error(f"❌ **Issue:** No data accessible or unexpected region access.")
            else:
                st.warning(f"⚠️ **No data returned** - This could mean:\n"
                          f"- The role {st.session_state.selected_role} has no data for {expected_region}\n"
                          f"- Role switching didn't work properly\n"
                          f"- Tables are empty or inaccessible")
        else:
            if not data_result.empty:
                st.info(f"ℹ️ **Full Access Mode:** You can see data from {len(data_result)} regions.")
            else:
                st.warning("⚠️ **No data returned** - This could indicate table access issues.")
 
    except Exception as e:
        st.error(f"❌ Role test failed: {str(e)}")
        st.info("💡 **Possible issues:**\n"
               "- Tables don't exist or aren't accessible\n"
               "- Database/schema permissions missing\n"
               "- Row access policies preventing access\n"
               "- Network connectivity issues")
 
 
def debug_data_access():
    """Debug function to check data access and identify issues."""
    global session
    st.subheader("🔍 Data Access Debug Information")
 
    try:
        # Check current session info
        st.write("**1. Session Information:**")
        session_info = session.sql("SELECT CURRENT_USER(), CURRENT_ROLE(), CURRENT_DATABASE(), CURRENT_SCHEMA()").to_pandas()
        st.dataframe(session_info, use_container_width=True)
 
        # Check if database and schema exist
        st.write("**2. Database and Schema Check:**")
        try:
            db_check = session.sql("SHOW DATABASES LIKE 'TEST_DB'").to_pandas()
            if not db_check.empty:
                st.success("✅ TEST_DB database exists")
 
                schema_check = session.sql("SHOW SCHEMAS LIKE 'PUBLIC' IN DATABASE TEST_DB").to_pandas()
                if not schema_check.empty:
                    st.success("✅ TEST_DB schema exists")
                else:
                    st.error("❌ PUBLIC schema not found")
            else:
                st.error("❌ TEST_DB database not found")
        except Exception as e:
            st.error(f"❌ Database/Schema check failed: {str(e)}")
 
        # Check tables
        st.write("**3. Table Existence Check:**")
        tables_to_check = ['SALES', 'RETURN', 'REPRESENTATIVES']
        for table in tables_to_check:
            try:
                table_check = session.sql(f"SHOW TABLES LIKE '{table}' IN SCHEMA TEST_DB.PUBLIC").to_pandas()
                if not table_check.empty:
                    st.success(f"✅ {table} table exists")
 
                    # Try to get a count
                    count_result = session.sql(f"SELECT COUNT(*) as count FROM TEST_DB.PUBLIC.{table}").to_pandas()
                    st.info(f"📊 {table} has {count_result.iloc[0]['COUNT']} records")
                else:
                    st.error(f"❌ {table} table not found")
            except Exception as e:
                st.error(f"❌ {table} table check failed: {str(e)}")
 
        # Check row access policies
        st.write("**4. Row Access Policy Check:**")
        try:
            policy_check = session.sql("SHOW ROW ACCESS POLICIES").to_pandas()
            if not policy_check.empty:
                st.success("✅ Row access policies found:")
                st.dataframe(policy_check, use_container_width=True)
            else:
                st.warning("⚠️ No row access policies found")
        except Exception as e:
            st.error(f"❌ Policy check failed: {str(e)}")
 
        # Check semantic model stage
        st.write("**5. Semantic Model Stage Check:**")
        try:
            stage_check = session.sql("SHOW STAGES LIKE 'MY_STAGE' IN SCHEMA TEST_DB.PUBLIC").to_pandas()
            if not stage_check.empty:
                st.success("✅ MY_STAGE exists in TEST_DB.PUBLIC")
 
                # List files in stage
                files_check = session.sql("LIST @TEST_DB.PUBLIC.MY_STAGE").to_pandas()
                st.info("📁 Files in stage:")
                st.dataframe(files_check, use_container_width=True)
            else:
                st.error("❌ MY_STAGE not found in TEST_DB.PUBLIC")
        except Exception as e:
            st.error(f"❌ Stage check failed: {str(e)}")
 
        # Test a simple query
        st.write("**6. Simple Query Test:**")
        try:
            simple_query = "SELECT 1 as test_value"
            simple_result = session.sql(simple_query).to_pandas()
            st.success("✅ Basic query execution works")
            st.dataframe(simple_result, use_container_width=True)
        except Exception as e:
            st.error(f"❌ Basic query failed: {str(e)}")
 
    except Exception as e:
        st.error(f"❌ Debug function failed: {str(e)}")
 
 
def test_accountadmin_access():
    """Test data access as ACCOUNTADMIN and provide solutions."""
    global session
    st.subheader("🚑 ACCOUNTADMIN Access Test")
 
    try:
        # Check current role
        current_role = session.sql("SELECT CURRENT_ROLE() as role").to_pandas()
        st.info(f"Current role: {current_role.iloc[0]['ROLE']}")
 
        # Check if ACCOUNTADMIN can see data without row access policies
        st.write("**Testing ACCOUNTADMIN data access:**")
 
        # Try to access data directly
        try:
            direct_query = "SELECT COUNT(*) as total_records FROM TEST_DB.PUBLIC.SALES"
            direct_result = session.sql(direct_query).to_pandas()
            total_records = direct_result.iloc[0]['TOTAL_RECORDS']
 
            if total_records > 0:
                st.success(f"✅ Found {total_records} total records in SALES table")
 
                # Try to get region breakdown
                region_query = "SELECT REGION, COUNT(*) as count FROM TEST_DB.PUBLIC.SALES GROUP BY REGION ORDER BY REGION"
                region_result = session.sql(region_query).to_pandas()
 
                if not region_result.empty:
                    st.success("✅ Can access regional data:")
                    st.dataframe(region_result, use_container_width=True)
 
                    st.markdown("""
                    **🎉 Good News!** ACCOUNTADMIN can access the data. The issue might be:
 
                    1. **Row Access Policies**: ACCOUNTADMIN might be excluded from the policies
                    2. **Cortex Analyst**: The semantic model might have issues
                    3. **Query Generation**: Cortex might be generating incorrect queries
 
                    **Next Steps:**
                    1. Try asking a simple question like "How many sales records do we have?"
                    2. Check if the semantic model file is correctly uploaded
                    3. Verify the Cortex Analyst is working
                    """)
                else:
                    st.warning("⚠️ No regional data returned - row access policies might be blocking ACCOUNTADMIN")
 
            else:
                st.error("❌ No records found in SALES table")
 
        except Exception as e:
            st.error(f"❌ Cannot access SALES table: {str(e)}")
 
            # Provide troubleshooting steps
            st.markdown("""
            **🔧 Troubleshooting Steps:**
 
            1. **Check if tables exist:**
            ```sql
            SHOW TABLES IN SCHEMA TEST_DB.PUBLIC;
            ```
 
            2. **Check permissions:**
            ```sql
            SHOW GRANTS ON TABLE TEST_DB.PUBLIC.SALES;
            ```
 
            3. **Check row access policies:**
            ```sql
            SHOW ROW ACCESS POLICIES;
            ```
 
            4. **Grant access to ACCOUNTADMIN (if needed):**
            ```sql
            GRANT SELECT ON TABLE TEST_DB.PUBLIC.SALES TO ROLE ACCOUNTADMIN;
            ```
            """)
 
    except Exception as e:
        st.error(f"❌ ACCOUNTADMIN test failed: {str(e)}")
 
 
def find_data_location():
    """Search for data in different databases and schemas."""
    global session
    st.subheader("🔍 Finding Your Data")
 
    try:
        # Search for databases
        st.write("**1. Searching for databases with 'SALES' or similar tables:**")
 
        databases_query = "SHOW DATABASES"
        databases = session.sql(databases_query).to_pandas()
 
        st.write(f"Found {len(databases)} databases:")
        st.write("**Available columns:**", list(databases.columns))
        st.dataframe(databases, use_container_width=True)
 
        # Get the correct column name for database names
        db_name_col = None
        for col in databases.columns:
            if 'name' in col.lower():
                db_name_col = col
                break
 
        if db_name_col is None:
            st.error("Could not find database name column")
            return
 
        # Search for tables named SALES in different locations
        st.write("**2. Searching for SALES tables across databases:**")
 
        sales_tables_found = []
 
        for db_name in databases[db_name_col]:
            try:
                # Get schemas in this database
                schemas_query = f"SHOW SCHEMAS IN DATABASE {db_name}"
                schemas = session.sql(schemas_query).to_pandas()
 
                # Get the correct column name for schema names
                schema_name_col = None
                for col in schemas.columns:
                    if 'name' in col.lower():
                        schema_name_col = col
                        break
 
                if schema_name_col is None:
                    continue
 
                for schema_name in schemas[schema_name_col]:
                    try:
                        # Look for SALES table in this schema
                        tables_query = f"SHOW TABLES LIKE 'SALES' IN SCHEMA {db_name}.{schema_name}"
                        tables = session.sql(tables_query).to_pandas()
 
                        if not tables.empty:
                            # Check record count
                            count_query = f"SELECT COUNT(*) as count FROM {db_name}.{schema_name}.SALES"
                            count_result = session.sql(count_query).to_pandas()
                            record_count = count_result.iloc[0]['COUNT']
 
                            sales_tables_found.append({
                                'Database': db_name,
                                'Schema': schema_name,
                                'Table': 'SALES',
                                'Records': record_count,
                                'Full_Path': f"{db_name}.{schema_name}.SALES"
                            })
 
                    except Exception:
                        continue  # Skip inaccessible schemas
 
            except Exception:
                continue  # Skip inaccessible databases
 
        if sales_tables_found:
            st.success(f"✅ Found {len(sales_tables_found)} SALES tables:")
            sales_df = pd.DataFrame(sales_tables_found)
            st.dataframe(sales_df, use_container_width=True)
 
            # Show tables with data
            tables_with_data = sales_df[sales_df['Records'] > 0]
            if not tables_with_data.empty:
                st.success("🎉 **Tables with data found!**")
                st.dataframe(tables_with_data, use_container_width=True)
 
                # Suggest updating the semantic model
                best_table = tables_with_data.iloc[0]
                st.markdown(f"""
                **💡 Suggested Fix:**
 
                Your data appears to be in: `{best_table['Full_Path']}`
 
                **Update your semantic model YAML file:**
                1. Change the database from `TEST_DB` to `{best_table['Database']}`
                2. Change the schema from `PUBLIC` to `{best_table['Schema']}`
                3. Re-upload the YAML file to your stage
 
                **Or update your app.py to use the correct location.**
                """)
            else:
                st.warning("⚠️ Found SALES tables but they're all empty")
        else:
            st.error("❌ No SALES tables found in any accessible database/schema")
 
        # Also search for sample data or test tables
        st.write("**3. Searching for sample/test data:**")
        sample_patterns = ['SAMPLE', 'TEST', 'DEMO', 'EXAMPLE']
 
        for pattern in sample_patterns:
            try:
                sample_query = f"SHOW TABLES LIKE '%{pattern}%'"
                sample_tables = session.sql(sample_query).to_pandas()
                if not sample_tables.empty:
                    st.info(f"Found {len(sample_tables)} tables matching '{pattern}':")
                    # Show all columns to see what's available
                    st.dataframe(sample_tables, use_container_width=True)
            except Exception:
                continue
 
    except Exception as e:
        st.error(f"❌ Data search failed: {str(e)}")
 
        st.markdown("""
        **🔧 Manual Steps to Find Your Data:**
 
        1. **Check all databases:**
        ```sql
        SHOW DATABASES;
        ```
 
        2. **Look for your data:**
        ```sql
        -- Replace DATABASE_NAME with actual database names
        SHOW TABLES IN DATABASE DATABASE_NAME;
        ```
 
        3. **Check for data:**
        ```sql
        SELECT COUNT(*) FROM DATABASE_NAME.SCHEMA_NAME.TABLE_NAME;
        ```
 
        4. **Look for sample data:**
        ```sql
        SHOW TABLES LIKE '%SALES%';
        SHOW TABLES LIKE '%SAMPLE%';
        ```
        """)
 
 
def show_header_and_sidebar():
    """Display the header and sidebar of the app.🤖"""
    st.title("❄️ SalesBot ❄️")
 
    # Show current role status prominently in the main area
    if st.session_state.selected_role:
        role_name = st.session_state.selected_role
        region = AVAILABLE_ROLES.get(role_name, "Unknown")
        st.success(f"🔐 **Active Role:** {region} Regional Head - You will see data filtered for {region} region only")
    else:
        st.info("🔓 **Access Mode:** Full Access - You can see all regional data")
 
    st.markdown(
        """
 
        **Ask. Analyze. Act.**
        Get real-time sales insights with the power of *Cortex Analyst* under the hood.
        One question away from unlocking your insights 💡
        """,
        unsafe_allow_html=True
    )
 
 
    with st.sidebar:
        # Role Selection Section
        st.subheader("🔐 Role Selection")
 
        # Display current role status
        st.info(get_current_role_info())
 
        # Role selector
        role_options = ["None (Full Access)"] + [f"{region} ({role})" for role, region in AVAILABLE_ROLES.items()]
        selected_role_display = st.selectbox(
            "Select your role:",
            role_options,
            index=0,
            help="Select a regional role to filter data based on your access permissions. "
                 "Each role will only show data for its specific region due to row access policies."
        )
 
        # Show role information
        with st.expander("ℹ️ About Role-Based Access", expanded=False):
            st.markdown("""
            **How Role-Based Access Works:**
 
            🔐 **Regional Roles**: Each role (e.g., HRZN_WESTERN_US_HEAD) can only access data for their specific region
 
            📊 **Filtered Data**: When you select a role, all queries will automatically filter to show only that region's data
 
            🛡️ **Row Access Policies**: This is enforced at the database level using Snowflake Row Access Policies
 
            🔓 **Full Access**: Select "None" to see data from all regions (if you have the necessary permissions)
 
            **Available Regions:**
            """ + "\n".join([f"• {region}" for region in sorted(set(AVAILABLE_ROLES.values()))]))
 
        # Manual role switching instructions
        if st.session_state.selected_role and st.session_state.selected_role != "ACCOUNTADMIN":
            with st.expander("🔧 Manual Role Switching Required", expanded=True):
                role_name = st.session_state.selected_role
                region = AVAILABLE_ROLES.get(role_name, "Unknown")
 
                st.warning(f"⚠️ **Current Issue**: You're still running as ACCOUNTADMIN instead of {role_name}")
 
                st.markdown(f"""
                **To fix this and see {region} data:**
 
                1. **Open a new Snowflake worksheet** or SQL editor
 
                2. **Run this command:**
                ```sql
                USE ROLE {role_name};
                ```
 
                3. **Verify the role switch:**
                ```sql
                SELECT CURRENT_ROLE();
                ```
 
                4. **Test data access:**
                ```sql
                SELECT REGION, COUNT(*)
                FROM TEST_DB.PUBLIC.SALES
                GROUP BY REGION;
                ```
 
                5. **Then return to this app** and try your questions again
 
                **Expected Result**: You should only see data for {region} region.
                """)
 
                if st.button(f"📋 Copy Role Switch Command", key="copy_role_cmd"):
                    st.code(f"USE ROLE {role_name};", language="sql")
                    st.success("✅ Command ready to copy!")
 
 
        # Handle role switching
        if selected_role_display != "None (Full Access)":
            # Extract role name from display string
            role_name = selected_role_display.split("(")[1].rstrip(")")
            if st.session_state.selected_role != role_name:
                if switch_role(role_name):
                    reset_session_state()  # Reset chat when role changes
        else:
            if st.session_state.selected_role is not None:
                st.session_state.selected_role = None
                st.success("✅ Switched to full access mode")
                reset_session_state()  # Reset chat when role changes
 
        st.divider()
 
        # Semantic Model Selection
        st.selectbox(
            "Selected semantic model:",
            AVAILABLE_SEMANTIC_MODELS_PATHS,
            format_func=lambda s: s.split("/")[-1],
            key="selected_semantic_model_path",
            on_change=reset_session_state,
        )
        st.divider()
 
        # _, btn_container, _ = st.columns([2, 6, 2])
        if st.button("Clear Chat History", use_container_width=True):
            reset_session_state()
 
        # Test role functionality
        if st.button("🧪 Test Current Role", use_container_width=True):
            test_current_role()
 
        # Debug data access
        if st.button("🔍 Debug Data Access", use_container_width=True):
            debug_data_access()
 
        # Quick fix for ACCOUNTADMIN
        if st.button("🚑 Emergency: Test as ACCOUNTADMIN", use_container_width=True):
            test_accountadmin_access()
 
        # Find data location
        if st.button("🔍 Find My Data", use_container_width=True):
            find_data_location()
 
        # Only show "Save as PDF" if messages exist
        if st.session_state.messages:
            pdf_data = generate_chat_pdf(st.session_state.messages)
            st.download_button(
                label=" Save Chat (PDF)",
                data=pdf_data,
                file_name=f"chat_history_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf",
                mime="application/pdf",
                use_container_width=True
            )
        if st.session_state.messages:
            if st.button("🧠 Smart Summary Insights", use_container_width=True):
                with st.spinner("Generating smart summary via Cortex..."):
                    summary = generate_smart_summary(st.session_state.messages)
                    if summary:
                        st.success("Smart Summary:")
                        st.markdown(summary)
                    else:
                        st.error("Failed to generate summary.")
 
        # Sample queries section
        with st.expander("💡 Sample Queries to Try", expanded=False):
            st.markdown("""
            **Try these sample questions to test role-based filtering:**
 
            📊 **Sales Analysis:**
            • "Show me total sales by region"
            • "What are the top 10 products by sales?"
            • "Show sales trends over time"
 
            🔍 **Regional Insights:**
            • "How many customers do we have?"
            • "What's the average order value?"
            • "Show me sales by category"
 
            📈 **Performance Metrics:**
            • "What's our profit margin by product category?"
            • "Show me return rates"
            • "Which shipping mode is most popular?"
 
            **Note:** Results will be automatically filtered based on your selected role!
            """)
                       
def handle_user_inputs():
    """Handle user inputs from the chat interface."""
    # Handle chat input
    user_input = st.chat_input("What is your question?")
    if user_input:
        process_user_input(user_input)
    # Handle suggested question click
    elif st.session_state.active_suggestion is not None:
        suggestion = st.session_state.active_suggestion
        st.session_state.active_suggestion = None
        process_user_input(suggestion)
 
 
 
 
def handle_error_notifications():
    if st.session_state.get("fire_API_error_notify"):
        st.toast("An API error has occurred!", icon="🚨")
        st.session_state["fire_API_error_notify"] = False
 
 
def process_user_input(prompt: str):
    """
    Process user input and update the conversation history.
 
    Args:
        prompt (str): The user's input.
    """
 
    # Create a new message, append to history and display immediately
    new_user_message = {
        "role": "user",
        "content": [{"type": "text", "text": prompt}],
    }
    st.session_state.messages.append(new_user_message)
    with st.chat_message("user"):
        user_msg_index = len(st.session_state.messages) - 1
        display_message(new_user_message["content"], user_msg_index)
 
    # Show progress indicator inside analyst chat message while waiting for response
    with st.chat_message("analyst"):
        with st.spinner("Waiting for Analyst's response..."):
            time.sleep(1)
            response, error_msg = get_analyst_response(st.session_state.messages)
            if error_msg is None:
                analyst_message = {
                    "role": "analyst",
                    "content": response["message"]["content"],
                    "request_id": response["request_id"],
                }
            else:
                analyst_message = {
                    "role": "analyst",
                    "content": [{"type": "text", "text": error_msg}],
                    "request_id": response["request_id"],
                }
                st.session_state["fire_API_error_notify"] = True
            st.session_state.messages.append(analyst_message)
            st.rerun()
 
 
def get_analyst_response(messages: List[Dict]) -> Tuple[Dict, Optional[str]]:
    """
    Send chat history to the Cortex Analyst API and return the response.
 
    Args:
        messages (List[Dict]): The conversation history.
 
    Returns:
        Tuple[Optional[Dict], Optional[str]]: The response from the Cortex Analyst API.
    """
    # Prepare the request body with the user's prompt
    request_body = {
        "messages": messages,
        "semantic_model_file": f"{st.session_state.selected_semantic_model_path}",
    }
 
    # Send a POST request to the Cortex Analyst API endpoint
    resp = _snowflake.send_snow_api_request(
        "POST",  # method
        API_ENDPOINT,  # path
        {},  # headers
        {},  # params
        request_body,  # body
        None,  # request_guid
        API_TIMEOUT,  # timeout in milliseconds
    )
 
    # Content is a string with serialized JSON object
    parsed_content = json.loads(resp["content"])
 
    # Check if the response is successful
    if resp["status"] < 400:
        return parsed_content, None
    else:
        # Craft readable error message
        error_msg = f"""
        An Analyst API error has occurred 🚨
 
* Response code: {resp['status']}
* Request ID: {parsed_content['request_id']}
* Error code: {parsed_content['error_code']}
 
Message:
        """
        return parsed_content, error_msg
 
 
def display_conversation():
    """
    Display the conversation history between the user and the assistant.
    """
    for idx, message in enumerate(st.session_state.messages):
        role = message["role"]
        content = message["content"]
        with st.chat_message(role):
            display_message(content, idx)
 
 
def display_message(content: List[Dict[str, str]], message_index: int):
    """
    Display a single message content.
 
    Args:
        content (List[Dict[str, str]]): The message content.
        message_index (int): The index of the message.
    """
    for item in content:
        if item["type"] == "text":
            st.markdown(item["text"])
        elif item["type"] == "suggestions":
            # Display suggestions as buttons
            for suggestion_index, suggestion in enumerate(item["suggestions"]):
                if st.button(
                    suggestion, key=f"suggestion_{message_index}_{suggestion_index}"
                ):
                    st.session_state.active_suggestion = suggestion
        elif item["type"] == "sql":
            # Display the SQL query and results
            display_sql_query(item["statement"], message_index)
        else:
            # Handle other content types if necessary
            pass
 
 
@st.cache_data(show_spinner=False)
def get_query_exec_result(query: str) -> Tuple[Optional[pd.DataFrame], Optional[str]]:
    """
    Execute the SQL query and convert the results to a pandas DataFrame.
 
    Args:
        query (str): The SQL query.
 
    Returns:
        Tuple[Optional[pd.DataFrame], Optional[str]]: The query results and any error message.
    """
    global session
    try:
        df = session.sql(query).to_pandas()
        return df, None
    except SnowparkSQLException as e:
        return None, str(e)
 
def generate_query_explanation(sql: str) -> str:
    """
    Generate a general explanation of the SQL query.
 
    Args:
        sql (str): The SQL query.
 
    Returns:
        str: A human-readable explanation of what the query does.
    """
    explanation = "🧐 **SQL Query Explanation:**\n"
 
    # Basic query type detection
    if "SELECT" in sql.upper():
        explanation += "- This query is retrieving data from a table.\n"
        if "WHERE" in sql.upper():
            explanation += "- It includes filtering conditions using a WHERE clause.\n"
        if "GROUP BY" in sql.upper():
            explanation += "- The query is aggregating data based on specific columns using GROUP BY.\n"
        if "ORDER BY" in sql.upper():
            explanation += "- The results are being sorted based on the ORDER BY clause.\n"
        if "JOIN" in sql.upper():
            explanation += "- Multiple tables are being combined using a JOIN operation.\n"
 
    elif "INSERT" in sql.upper():
        explanation += "- This query is inserting new data into a table.\n"
   
    elif "UPDATE" in sql.upper():
        explanation += "- This query is modifying existing records in a table.\n"
   
    elif "DELETE" in sql.upper():
        explanation += "- This query is removing data from a table.\n"
 
    explanation += "\n✅ The SQL query is structured to retrieve and process relevant information based on the specified conditions."
 
    return explanation.strip()
 
def display_sql_query(sql: str, message_index: int):
    """
    Execute the SQL query and display the results in the form of a dataframe, charts, and an explanation.
 
    Args:
        sql (str): The SQL query.
        message_index (int): The index of the message.
    """
 
    # Display the SQL query
    with st.expander("SQL Query", expanded=False):
        st.code(sql, language="sql")
 
    # Display the results of the SQL query
    with st.expander("Results", expanded=True):
        # Show role-based filtering info
        if st.session_state.selected_role:
            role_name = st.session_state.selected_role
            region = AVAILABLE_ROLES.get(role_name, "Unknown")
            st.info(f"🔐 **Data filtered for:** {region} region (Role: {role_name})")
        else:
            st.info("🔓 **Showing:** All regional data (Full access mode)")
 
        with st.spinner("Running SQL..."):
            df, err_msg = get_query_exec_result(sql)
            if df is None:
                st.error(f"Could not execute generated SQL query. Error: {err_msg}")
                return
 
            if df.empty:
                st.write("Query returned no data")
                # Add helpful message for role-based filtering
                if st.session_state.selected_role:
                    region = AVAILABLE_ROLES.get(st.session_state.selected_role, "Unknown")
                    st.warning(f"💡 **Note:** No data found for {region} region. This might be because:\n"
                             f"- There's no data for this region in the selected time period\n"
                             f"- The query filters don't match any records for {region}\n"
                             f"- Try adjusting your query or switching to 'Full Access' mode to see all data")
                return
 
            # Show query results in three tabs
            data_tab, chart_tab, explanation_tab = st.tabs(["DATA", "CHART", "EXPLANATION"])
 
            with data_tab:
                # Show data count info
                if st.session_state.selected_role:
                    region = AVAILABLE_ROLES.get(st.session_state.selected_role, "Unknown")
                    st.success(f"📊 Found **{len(df)}** records for {region} region")
                else:
                    st.success(f"📊 Found **{len(df)}** records across all regions")
                st.dataframe(df, use_container_width=True)
 
            with chart_tab:
                display_charts_tab(df, message_index)
 
            with explanation_tab:
                query_explanation = generate_query_explanation(sql)
                st.markdown(query_explanation)
 
import pandas as pd
import streamlit as st
import plotly.express as px
 
def display_charts_tab(df: pd.DataFrame, message_index: int) -> None:
    """
    Display the charts tab.
 
    Args:
        df (pd.DataFrame): The query results.
        message_index (int): The index of the message.
    """
    if len(df.columns) >= 2:
        all_cols_set = set(df.columns)
        col1, col2 = st.columns(2)
        x_col = col1.selectbox(
            "X axis", all_cols_set, key=f"x_col_select_{message_index}"
        )
        y_col = col2.selectbox(
            "Y axis",
            all_cols_set.difference({x_col}),
            key=f"y_col_select_{message_index}",
        )
 
        chart_type = st.selectbox(
            "Select chart type",
            options=["Line Chart", "Bar Chart", "Pie Chart", "Bubble Map"],
            key=f"chart_type_{message_index}",
        )
 
        if chart_type == "Line Chart":
            st.line_chart(df.set_index(x_col)[y_col])
        elif chart_type == "Bar Chart":
            st.bar_chart(df.set_index(x_col)[y_col])
        elif chart_type == "Pie Chart":
            if pd.api.types.is_numeric_dtype(df[y_col]):
                st.plotly_chart(
                    px.pie(df, names=x_col, values=y_col, title="Pie Chart"),
                    use_container_width=True,
                )
            else:
                st.error("The selected Y-axis column must be numeric for a pie chart.")
       
    else:
        st.write("At least 2 columns are required")
 
 
from fpdf import FPDF
import io
 
class StyledPDF(FPDF):
    def __init__(self):
        super().__init__()
        self.set_auto_page_break(auto=True, margin=15)
        self.add_page()
        self.set_font("Arial", size=12)
 
    def add_message(self, role: str, text: str):
        if role.lower() == "user":
            self.set_text_color(0, 0, 255)  # Blue
            self.set_font("Arial", style="B", size=12)
            self.cell(0, 10, "User:", ln=True)
        elif role.lower() == "analyst":
            self.set_text_color(100, 100, 100)  # Gray
            self.set_font("Arial", style="B", size=12)
            self.cell(0, 10, "Analyst:", ln=True)
       
        self.set_font("Arial", size=12)
        self.set_text_color(0, 0, 0)  # Reset to black
        self.multi_cell(0, 10, text)
        self.ln(5)  # Add space after each message
 
def generate_chat_pdf(messages: List[Dict]) -> bytes:
    """Generate a styled PDF from the chat history."""
    pdf = StyledPDF()
 
    for msg in messages:
        role = msg["role"]
        for item in msg["content"]:
            if item["type"] == "text":
                pdf.add_message(role, item["text"])
            elif item["type"] == "sql":
                sql = item.get("statement", "")
                df, err = get_query_exec_result(sql)
                if df is not None and not df.empty:
                    sample = df.head(5).to_string(index=False)
                    pdf.add_message(role, f"Query Output (Top 5 rows):\n{sample}")
                elif err:
                    pdf.add_message(role, f"SQL Error: {err}")
    return bytes(pdf.output(dest="S").encode("latin1"))
 
 
def generate_smart_summary(messages: List[Dict]) -> Optional[str]:
    """
    Generate a smart summary of analyst messages and natural language summaries of SQL outputs.
    """
    from datetime import datetime
 
    def summarize_dataframe(df: pd.DataFrame) -> str:
        if df.empty:
            return "Query returned no data."
 
        summary_lines = []
 
        # Use column name and type heuristics
        num_cols = df.select_dtypes(include="number").columns
        str_cols = df.select_dtypes(include="object").columns
 
        if "total" in " ".join(col.lower() for col in df.columns):
            for col in num_cols:
                total = df[col].sum()
                summary_lines.append(f"Total {col.replace('_', ' ').title()}: {total:,.2f}")
        elif len(df.columns) == 2 and pd.api.types.is_numeric_dtype(df.dtypes[1]):
            summary_lines.append("Top breakdown:")
            for _, row in df.head(5).iterrows():
                key = row[0]
                value = row[1]
                summary_lines.append(f"- {key}: {value:,.2f}" if isinstance(value, (int, float)) else f"- {key}: {value}")
        elif "year" in df.columns.str.lower().tolist() and len(num_cols) == 1:
            metric = num_cols[0]
            summary_lines.append(f"Year-wise breakdown of {metric.replace('_', ' ').title()}:")
            for _, row in df.iterrows():
                summary_lines.append(f"- {int(row['YEAR'])}: {row[metric]:,.2f}")
        elif "product" in " ".join(col.lower() for col in df.columns):
            summary_lines.append("Sample of products:")
            for _, row in df.head(5).iterrows():
                summary_lines.append(f"- {row[0]}")
        else:
            summary_lines.append("Top 5 result rows:")
            for _, row in df.head(5).iterrows():
                summary_lines.append("- " + ", ".join(f"{col}: {val}" for col, val in row.items()))
 
        return "\n".join(summary_lines)
 
    analyst_texts = []
    sql_summaries = []
 
    for msg in messages:
        if msg["role"] == "analyst":
            for item in msg["content"]:
                if item["type"] == "text":
                    analyst_texts.append(item["text"])
                elif item["type"] == "sql":
                    sql = item.get("statement", "")
                    df, err = get_query_exec_result(sql)
                    if df is not None:
                        sql_summaries.append(summarize_dataframe(df))
                    elif err:
                        sql_summaries.append(f"SQL Error: {err}")
 
    final_summary = "**🧠 Smart Summary**\n\n"
 
    # if analyst_texts:
    #     final_summary += "**Analyst Insights:**\n"
    #     final_summary += "\n\n".join(analyst_texts)
    #     final_summary += "\n\n"
 
    if sql_summaries:
        final_summary += "**SQL Output Summaries:**\n"
        final_summary += "\n\n".join(sql_summaries)
 
    return final_summary.strip()
 
 
 
if __name__ == "__main__":
    main()
 